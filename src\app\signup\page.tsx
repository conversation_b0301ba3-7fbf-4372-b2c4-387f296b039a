"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Music } from "lucide-react"
import Link from "next/link"

export default function SignupPage() {
  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        <Card>
          <CardHeader className="text-center space-y-4">
            <div className="mx-auto w-12 h-12 bg-primary rounded-full flex items-center justify-center">
              <Music className="w-6 h-6 text-primary-foreground" />
            </div>
            <div>
              <CardTitle className="text-2xl">Join Smash Music</CardTitle>
              <CardDescription className="mt-2">Create your account to get started</CardDescription>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="text-center text-muted-foreground">
              <p>Signup functionality will be implemented in the next phase.</p>
              <p className="mt-2">For now, please use the login page if you have an existing account.</p>
            </div>

            <Button asChild className="w-full h-12 text-base" size="lg">
              <Link href="/login">Back to Login</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
