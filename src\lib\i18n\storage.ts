import type { Locale } from '@/i18n/config';

/**
 * Storage utilities for persisting locale preferences
 */
export class LocaleStorage {
  constructor(private storageKey: string) {}

  /**
   * Get the stored locale preference
   */
  getLocale(availableLocales: readonly Locale[], defaultLocale: Locale): Locale {
    if (typeof window === 'undefined') {
      return defaultLocale;
    }

    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored && availableLocales.includes(stored as Locale)) {
        return stored as Locale;
      }
    } catch (error) {
      console.warn('Failed to read locale from localStorage:', error);
    }

    return defaultLocale;
  }

  /**
   * Store the locale preference
   */
  setLocale(locale: Locale): void {
    if (typeof window === 'undefined') {
      return;
    }

    try {
      localStorage.setItem(this.storageKey, locale);
    } catch (error) {
      console.warn('Failed to save locale to localStorage:', error);
    }
  }

  /**
   * Clear the stored locale preference
   */
  clearLocale(): void {
    if (typeof window === 'undefined') {
      return;
    }

    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.warn('Failed to clear locale from localStorage:', error);
    }
  }
}
