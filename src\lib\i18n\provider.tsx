'use client';

import { createContext, useContext, useState, useEffect, useCallback, useMemo, type ReactNode } from 'react';
import { NextIntlClientProvider } from 'next-intl';
import type { Locale } from '@/i18n/config';
import type { StaticI18nConfig, StaticI18nContextValue } from './types';
import { LocaleStorage } from './storage';
import { defaultStaticI18nConfig } from './config';

const StaticI18nContext = createContext<StaticI18nContextValue | undefined>(undefined);

interface StaticI18nProviderProps {
  children: ReactNode;
  config?: Partial<StaticI18nConfig>;
}

/**
 * Provider component for the static i18n system
 * 
 * This component manages locale state and provides translations
 * without requiring server-side functionality.
 */
export function StaticI18nProvider({
  children,
  config: configOverrides = {}
}: StaticI18nProviderProps) {
  const config = { ...defaultStaticI18nConfig, ...configOverrides };
  const storage = useMemo(() => new LocaleStorage(config.storageKey!), [config.storageKey]);
  
  const [locale, setLocaleState] = useState<Locale>(config.defaultLocale);
  const [messages, setMessages] = useState<Record<string, unknown>>({});
  const [isLoading, setIsLoading] = useState(true);

  // Load messages for a specific locale
  const loadMessages = useCallback(async (targetLocale: Locale) => {
    try {
      const loadedMessages = await config.messageLoader!(targetLocale);
      setMessages(loadedMessages);
    } catch (error) {
      console.error(`Failed to load messages for locale: ${targetLocale}`, error);
      setMessages({});
    }
  }, [config.messageLoader]);

  // Initialize locale and messages on mount
  useEffect(() => {
    const initializeI18n = async () => {
      setIsLoading(true);

      const storedLocale = storage.getLocale(config.locales, config.defaultLocale);
      setLocaleState(storedLocale);

      await loadMessages(storedLocale);
      setIsLoading(false);
    };

    initializeI18n();
  }, [config.defaultLocale, config.locales, loadMessages, storage]);

  // Handle locale changes
  const setLocale = async (newLocale: Locale) => {
    if (newLocale === locale) return;
    
    setIsLoading(true);
    setLocaleState(newLocale);
    storage.setLocale(newLocale);
    
    await loadMessages(newLocale);
    setIsLoading(false);
  };

  const contextValue: StaticI18nContextValue = {
    locale,
    setLocale,
    isLoading,
    availableLocales: config.locales,
  };

  // Show loading state if configured
  if (isLoading && config.showLoadingState) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <StaticI18nContext.Provider value={contextValue}>
      <NextIntlClientProvider
        locale={locale}
        messages={messages}
        timeZone="UTC"
      >
        {children}
      </NextIntlClientProvider>
    </StaticI18nContext.Provider>
  );
}

/**
 * Hook to access the static i18n context
 */
export function useStaticI18nContext() {
  const context = useContext(StaticI18nContext);
  if (context === undefined) {
    throw new Error('useStaticI18nContext must be used within a StaticI18nProvider');
  }
  return context;
}
