import { Amplify } from 'aws-amplify';

export const cognitoConfig = {
  region: process.env.NEXT_PUBLIC_COGNITO_REGION || "us-east-2",
  userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID || "us-east-2_YourUserPoolId",
  clientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || "your-client-id",
  // Optional: Only needed if using Identity Pool for additional AWS services
  identityPoolId: process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID,
};

// Initialize Amplify with Cognito configuration for custom UI
export function initCognitoAuth() {
  const config = {
    Auth: {
      Cognito: {
        userPoolId: cognitoConfig.userPoolId,
        userPoolClientId: cognitoConfig.clientId,
        // Remove loginWith configuration to use default behavior
        // This allows Cognito to handle username/email automatically
      }
    }
  };

  // Add Identity Pool if provided (for additional AWS services)
  if (cognitoConfig.identityPoolId && cognitoConfig.identityPoolId !== 'us-east-2:xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx') {
    // TypeScript workaround for optional identityPoolId
    Object.assign(config.Auth.Cognito, { identityPoolId: cognitoConfig.identityPoolId });
  }

  console.log('Initializing Amplify with config:', {
    userPoolId: config.Auth.Cognito.userPoolId,
    userPoolClientId: config.Auth.Cognito.userPoolClientId,
    region: cognitoConfig.region
  });

  Amplify.configure(config);
}
