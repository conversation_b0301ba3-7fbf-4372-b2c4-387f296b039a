"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { Music } from "lucide-react"
import { LoginForm } from "@/components/auth/LoginForm"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth/AuthContext"
import { useEffect } from "react"
import Link from "next/link"
import type { AuthUser } from "@/lib/auth-utils"

export default function LoginPage() {
  const router = useRouter()
  const { isAuthenticated, refreshUser } = useAuth()

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, router])

  const handleLoginSuccess = async (user: AuthUser) => {
    console.log('Login successful:', user)
    await refreshUser()
    router.push('/dashboard')
  }

  const handleLoginError = (error: string) => {
    console.error('Login error:', error)
  }

  return (
    <div className="w-full max-w-md space-y-6">
      <Card>
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-12 h-12 bg-primary rounded-full flex items-center justify-center">
            <Music className="w-6 h-6 text-primary-foreground" />
          </div>
          <div>
            <CardTitle className="text-2xl">Welcome to Smash Music</CardTitle>
            <CardDescription className="mt-2">Please sign in to continue</CardDescription>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          <LoginForm
            onSuccess={handleLoginSuccess}
            onError={handleLoginError}
          />

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">Or</span>
            </div>
          </div>

          <Button
            asChild
            variant="outline"
            className="w-full h-12 text-base"
            size="lg"
          >
            <Link href="/signup">Create an account</Link>
          </Button>
        </CardContent>
      </Card>

      {/* Features Section */}
      <Card className="bg-muted/50">
        <CardContent className="pt-6">
          <div className="space-y-4">
            <h3 className="font-semibold text-center">Why join Smash Music?</h3>
            <div className="grid gap-3 text-sm">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary rounded-full" />
                <span>Discover new artists and music</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary rounded-full" />
                <span>Create and share playlists</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary rounded-full" />
                <span>Connect with your favorite artists</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary rounded-full" />
                <span>Access exclusive content</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
