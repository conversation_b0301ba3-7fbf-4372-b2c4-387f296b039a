# Modular I18n System for Static Export

A highly modular, scalable, and maintainable internationalization system designed for Next.js static export compatibility.

## 🎯 **Key Features**

- **✅ Static Export Compatible** - Works with `output: 'export'`
- **✅ Zero Server Actions** - Fully client-side implementation
- **✅ Modular Architecture** - Easy to enable/disable and extend
- **✅ Minimal Code Changes** - Drop-in replacement for existing setup
- **✅ Type Safe** - Full TypeScript support
- **✅ Scalable** - Easy to add new locales and features

## 🏗️ **Architecture Overview**

```
src/lib/i18n/
├── index.ts                    # Main exports
├── types.ts                    # TypeScript definitions
├── config.ts                   # Configuration utilities
├── storage.ts                  # Locale persistence
├── provider.tsx                # React context provider
├── hooks.ts                    # React hooks
├── hoc.tsx                     # Higher-order components
├── feature-flags.ts            # Feature toggles
├── components/
│   └── LanguageSelector.tsx    # Reusable language selector
└── adapters/
    └── layout-adapter.tsx      # Layout integration adapter
```

## 🚀 **Quick Start**

### 1. Basic Usage (Layout Integration)

Replace your existing i18n provider in `layout.tsx`:

```tsx
// Before
import { I18nProvider } from '@/contexts/i18n/I18nContext';

// After
import { LayoutI18nAdapter } from '@/lib/i18n';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <LayoutI18nAdapter>
          {/* Your other providers */}
          {children}
        </LayoutI18nAdapter>
      </body>
    </html>
  );
}
```

### 2. Language Selector Component

```tsx
import { LanguageSelector } from '@/lib/i18n';

function MyComponent() {
  return (
    <div>
      <LanguageSelector />
      {/* or variants */}
      <CompactLanguageSelector />
      <MinimalLanguageSelector />
    </div>
  );
}
```

### 3. Using Hooks

```tsx
import { useStaticLocale } from '@/lib/i18n';

function MyComponent() {
  const { locale, setLocale, isLoading, availableLocales } = useStaticLocale();
  
  return (
    <div>
      <p>Current locale: {locale}</p>
      <button onClick={() => setLocale('es')}>
        Switch to Spanish
      </button>
    </div>
  );
}
```

## 🔧 **Advanced Configuration**

### Custom Configuration

```tsx
import { LayoutI18nAdapter, createStaticI18nConfig } from '@/lib/i18n';

const customConfig = createStaticI18nConfig({
  defaultLocale: 'en',
  locales: ['en', 'es', 'fr', 'de'],
  storageKey: 'MY_APP_LOCALE',
  showLoadingState: false,
});

function Layout({ children }) {
  return (
    <LayoutI18nAdapter config={customConfig}>
      {children}
    </LayoutI18nAdapter>
  );
}
```

### Custom Message Loader

```tsx
const customMessageLoader = async (locale) => {
  // Load from API, database, or custom source
  const response = await fetch(`/api/messages/${locale}`);
  return response.json();
};

const config = createStaticI18nConfig({
  messageLoader: customMessageLoader,
});
```

### Feature Flags

```tsx
import { I18N_FEATURE_FLAGS } from '@/lib/i18n';

// Toggle features
I18N_FEATURE_FLAGS.USE_STATIC_I18N = true;
I18N_FEATURE_FLAGS.DEBUG_I18N = true;
I18N_FEATURE_FLAGS.SHOW_LOADING_STATES = false;
```

## 🧩 **Component Variants**

### HOC Pattern

```tsx
import { withStaticI18n } from '@/lib/i18n';

const MyComponent = ({ title }) => <h1>{title}</h1>;

export default withStaticI18n(MyComponent, {
  defaultLocale: 'en',
  showLoadingState: true,
});
```

### Custom Language Selector

```tsx
import { LanguageSelector } from '@/lib/i18n';

function CustomLanguageSelector() {
  return (
    <LanguageSelector
      variant="minimal"
      customLabels={{
        en: 'English',
        es: 'Español',
        fr: 'Français',
      }}
      className="my-custom-class"
    />
  );
}
```

## 📦 **Migration Guide**

### From Previous Implementation

1. **Remove old files** (optional):
   ```bash
   rm -rf src/contexts/i18n/
   ```

2. **Update layout.tsx**:
   ```tsx
   // Replace
   import { I18nProvider } from '@/contexts/i18n/I18nContext';
   
   // With
   import { LayoutI18nAdapter } from '@/lib/i18n';
   ```

3. **Update language toggle**:
   ```tsx
   // Replace custom implementation
   // With
   import { LanguageSelector } from '@/lib/i18n';
   ```

### Backward Compatibility

The system is designed to work alongside existing next-intl setup:

- Original `src/i18n/` files remain unchanged
- Can be toggled via feature flags
- Gradual migration possible

## 🎛️ **API Reference**

### Hooks

- `useStaticLocale()` - Main locale management hook
- `useLocaleInfo()` - Read-only locale information
- `useLocaleChange(callback)` - React to locale changes

### Components

- `<LanguageSelector />` - Full-featured language selector
- `<CompactLanguageSelector />` - Compact variant
- `<MinimalLanguageSelector />` - Minimal variant

### HOCs

- `withStaticI18n(Component, config)` - Wrap component with i18n
- `createStaticI18nHOC(config)` - Pre-configured HOC factory

### Utilities

- `createStaticI18nConfig(overrides)` - Configuration factory
- `LocaleStorage` - Storage management class

## 🔍 **Troubleshooting**

### Build Errors

If you encounter Server Actions errors:
```tsx
// Ensure feature flag is enabled
import { I18N_FEATURE_FLAGS } from '@/lib/i18n';
I18N_FEATURE_FLAGS.USE_STATIC_I18N = true;
```

### Missing Translations

```tsx
// Check message loader configuration
const config = createStaticI18nConfig({
  messageLoader: async (locale) => {
    console.log(`Loading messages for: ${locale}`);
    return await import(`../../../messages/${locale}.json`);
  },
});
```

### Performance Issues

```tsx
// Disable loading states for better performance
const config = createStaticI18nConfig({
  showLoadingState: false,
});
```

## 🚀 **Benefits**

- **Modular**: Each feature is self-contained
- **Scalable**: Easy to add new locales and features
- **Maintainable**: Clear separation of concerns
- **Type Safe**: Full TypeScript support
- **Flexible**: Multiple integration patterns
- **Performance**: Optimized for static export
