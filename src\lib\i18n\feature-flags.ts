/**
 * Feature flags for the i18n system
 * 
 * This allows easy switching between the old next-intl server setup
 * and the new static export compatible system.
 */

export const I18N_FEATURE_FLAGS = {
  /** Use the new static i18n system instead of next-intl server functions */
  USE_STATIC_I18N: true,
  
  /** Enable debug logging for i18n operations */
  DEBUG_I18N: process.env.NODE_ENV === 'development',
  
  /** Show loading states during locale changes */
  SHOW_LOADING_STATES: true,
  
  /** Use localStorage for persistence (vs cookies) */
  USE_LOCAL_STORAGE: true,
  
  /** Enable automatic locale detection from browser */
  AUTO_DETECT_LOCALE: false,
} as const;

/**
 * Runtime configuration that can be modified
 */
export const I18N_RUNTIME_CONFIG = {
  /** Storage key for locale preference */
  STORAGE_KEY: 'NEXT_LOCALE',
  
  /** Timeout for loading messages (ms) */
  LOADING_TIMEOUT: 5000,
  
  /** Whether to fallback to default locale on error */
  FALLBACK_TO_DEFAULT: true,
} as const;

/**
 * Helper to check if static i18n should be used
 */
export const shouldUseStaticI18n = () => I18N_FEATURE_FLAGS.USE_STATIC_I18N;

/**
 * Helper to check if debug mode is enabled
 */
export const isI18nDebugEnabled = () => I18N_FEATURE_FLAGS.DEBUG_I18N;
