import type { Locale } from '@/i18n/config';

/**
 * Configuration for the static i18n system
 */
export interface StaticI18nConfig {
  /** Default locale to use */
  defaultLocale: Locale;
  /** Available locales */
  locales: readonly Locale[];
  /** Storage key for persisting locale preference */
  storageKey?: string;
  /** Whether to show loading state during initialization */
  showLoadingState?: boolean;
  /** Custom message loader function */
  messageLoader?: MessageLoader;
}

/**
 * Function to load messages for a specific locale
 */
export type MessageLoader = (locale: Locale) => Promise<Record<string, unknown>> | Record<string, unknown>;

/**
 * Context value for the static i18n provider
 */
export interface StaticI18nContextValue {
  /** Current locale */
  locale: Locale;
  /** Function to change locale */
  setLocale: (locale: Locale) => void;
  /** Whether the system is currently loading */
  isLoading: boolean;
  /** Available locales */
  availableLocales: readonly Locale[];
}

/**
 * Props for components that can be wrapped with static i18n
 */
export interface WithStaticI18nProps {
  locale?: Locale;
}
