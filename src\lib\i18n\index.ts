/**
 * Modular I18n System for Static Export
 *
 * This module provides a drop-in replacement for next-intl server functions
 * that works with static export. It's designed to be:
 * - Modular: Can be easily enabled/disabled
 * - Scalable: Easy to add new locales and features
 * - Maintainable: Minimal changes to existing code
 */

// Core exports
export { StaticI18nProvider } from './provider';
export { useStaticLocale, useLocaleInfo, useLocaleChange } from './hooks';
export { createStaticI18nConfig, defaultStaticI18nConfig } from './config';
export { withStaticI18n, createStaticI18nHOC } from './hoc';

// Components
export { LanguageSelector, CompactLanguageSelector, MinimalLanguageSelector } from './components/LanguageSelector';

// Adapters
export { LayoutI18nAdapter, withLayoutI18n } from './adapters/layout-adapter';

// Feature flags and configuration
export { I18N_FEATURE_FLAGS, I18N_RUNTIME_CONFIG, shouldUseStaticI18n } from './feature-flags';

// Storage utilities
export { LocaleStorage } from './storage';

// Re-export types for convenience
export type { Locale } from '@/i18n/config';
export type { StaticI18nConfig, MessageLoader, StaticI18nContextValue, WithStaticI18nProps } from './types';
