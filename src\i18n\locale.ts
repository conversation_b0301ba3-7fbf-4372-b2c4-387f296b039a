'use client';

import {Locale, defaultLocale} from '@/i18n/config';

// For static export, we use localStorage instead of cookies
const STORAGE_KEY = 'NEXT_LOCALE';

export function getUserLocale(): Locale {
  if (typeof window === 'undefined') {
    return defaultLocale;
  }

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored && ['en', 'es', 'fr'].includes(stored)) {
      return stored as Locale;
    }
  } catch (error) {
    console.warn('Failed to read locale from localStorage:', error);
  }

  return defaultLocale;
}

export function setUserLocale(locale: Locale): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.setItem(STORAGE_KEY, locale);
    // Trigger a page reload to apply the new locale
    window.location.reload();
  } catch (error) {
    console.warn('Failed to save locale to localStorage:', error);
  }
}