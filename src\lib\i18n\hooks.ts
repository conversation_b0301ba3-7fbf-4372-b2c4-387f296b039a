'use client';

import { useEffect } from 'react';
import { useStaticI18nContext } from './provider';
import type { Locale } from '@/i18n/config';

/**
 * Hook to get and set the current locale
 * 
 * This is a drop-in replacement for server-side locale functions
 * that works with static export.
 */
export function useStaticLocale() {
  const { locale, setLocale, isLoading, availableLocales } = useStaticI18nContext();

  return {
    /** Current locale */
    locale,
    /** Change the current locale */
    setLocale,
    /** Whether locale is being changed */
    isLoading,
    /** All available locales */
    availableLocales,
    /** Check if a locale is available */
    isLocaleAvailable: (checkLocale: Locale) => availableLocales.includes(checkLocale),
  };
}

/**
 * Hook for locale-aware components that need to react to locale changes
 */
export function useLocaleChange(callback: (locale: Locale) => void) {
  const { locale } = useStaticI18nContext();

  // Use effect to call callback when locale changes
  useEffect(() => {
    callback(locale);
  }, [locale, callback]);
}

/**
 * Hook to get locale information without the ability to change it
 * Useful for read-only components
 */
export function useLocaleInfo() {
  const { locale, availableLocales, isLoading } = useStaticI18nContext();
  
  return {
    locale,
    availableLocales,
    isLoading,
    isLocaleAvailable: (checkLocale: Locale) => availableLocales.includes(checkLocale),
  };
}
