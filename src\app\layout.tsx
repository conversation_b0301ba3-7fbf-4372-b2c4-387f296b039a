import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/theme/ThemeContext";
import { AuthProvider } from "@/contexts/auth/AuthContext";
import { LayoutI18nAdapter } from "@/lib/i18n";
import { MusicPlayerProvider } from "@/contexts/music-player-context/music-player-context"
import Header from "@/components/shared/Header";
import { defaultLocale } from "@/i18n/config";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Smash Music",
  description: "A modern music application with dark/light theme support",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // For static export, we use the default locale
  // Client-side locale switching will be handled by the LanguageToggle component
  const locale = defaultLocale;

  return (
    <html lang={locale}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <LayoutI18nAdapter>
          <ThemeProvider>
            <AuthProvider>
              <MusicPlayerProvider>
                <Header />
                {children}
              </MusicPlayerProvider>
            </AuthProvider>
          </ThemeProvider>
        </LayoutI18nAdapter>
      </body>
    </html>
  );
}
