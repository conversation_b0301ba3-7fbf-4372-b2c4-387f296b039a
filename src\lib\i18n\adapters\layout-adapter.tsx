'use client';

import { ReactNode } from 'react';
import { StaticI18nProvider } from '../provider';
import type { StaticI18nConfig } from '../types';

interface LayoutI18nAdapterProps {
  children: ReactNode;
  config?: Partial<StaticI18nConfig>;
}

/**
 * Layout adapter for the static i18n system
 * 
 * This component provides a drop-in replacement for the existing
 * i18n setup in the layout. It can be easily swapped in/out.
 */
export function LayoutI18nAdapter({ children, config }: LayoutI18nAdapterProps) {
  return (
    <StaticI18nProvider config={config}>
      {children}
    </StaticI18nProvider>
  );
}

/**
 * HOC version for wrapping the entire app
 */
export function withLayoutI18n(config?: Partial<StaticI18nConfig>) {
  return function LayoutWrapper({ children }: { children: ReactNode }) {
    return (
      <LayoutI18nAdapter config={config}>
        {children}
      </LayoutI18nAdapter>
    );
  };
}
