import { signIn, signOut, getCurrentUser, resetPassword as amplifyResetPassword, confirmResetPassword } from 'aws-amplify/auth'

// Helper function to try different username formats
async function trySignInWithDifferentFormats(email: string, password: string) {
  const attempts = [
    { format: 'username-from-email-lowercase', username: email.split('@')[0].toLowerCase() },
    { format: 'username-from-email-original', username: email.split('@')[0] },
    { format: 'email-lowercase', username: email.toLowerCase().trim() },
    { format: 'email-original-case', username: email.trim() },
  ]

  console.log('=== TRYING DIFFERENT USERNAME FORMATS ===')
  let lastError: Error | null = null

  for (const attempt of attempts) {
    try {
      console.log(`🔄 Attempt ${attempts.indexOf(attempt) + 1}: ${attempt.format}`)
      console.log(`   Username: "${attempt.username}"`)
      console.log(`   Password length: ${password.length} characters`)

      const result = await signIn({
        username: attempt.username,
        password: password,
      })

      console.log(`✅ SUCCESS with format: ${attempt.format}`)
      return result
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error)
      console.log(`❌ Failed with format: ${attempt.format} - ${errorMsg}`)
      lastError = error instanceof Error ? error : new Error(String(error))
      // Continue to next attempt
    }
  }

  console.log('=== ALL ATTEMPTS FAILED ===')
  // If all attempts fail, throw the last error
  throw lastError || new Error('All login attempts failed')
}

export interface LoginResponse {
  success: boolean
  error?: string
  user?: AuthUser
}

export interface AuthUser {
  userId: string
  username: string
  email?: string
}

export async function loginUser({
  email,
  password
}: {
  email: string
  password: string
}): Promise<LoginResponse> {
  try {
    console.log('=== LOGIN ATTEMPT ===')
    console.log('Email:', email)
    console.log('User Pool ID:', process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID)
    console.log('Client ID:', process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID)
    console.log('Region:', process.env.NEXT_PUBLIC_COGNITO_REGION)

    // Clean the email input
    const cleanEmail = email.toLowerCase().trim()
    console.log('Cleaned email:', cleanEmail)

    // Try different username formats
    console.log('Attempting sign in with different formats...')
    const { isSignedIn, nextStep } = await trySignInWithDifferentFormats(email, password)
    console.log('Sign in response:', { isSignedIn, nextStep })

    if (nextStep?.signInStep) {
      // Handle different auth challenges if needed
      switch (nextStep.signInStep) {
        case 'DONE':
          // Authentication is complete, continue to get user details
          console.log('✅ Authentication completed successfully')
          break
        case 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED':
          return {
            success: false,
            error: 'Please change your password before continuing'
          }
        case 'CONFIRM_SIGN_IN_WITH_SMS_CODE':
        case 'CONFIRM_SIGN_IN_WITH_TOTP_CODE':
          return {
            success: false,
            error: 'Please complete MFA verification'
          }
        default:
          return {
            success: false,
            error: `Authentication challenge required: ${nextStep.signInStep}`
          }
      }
    }

    if (isSignedIn || nextStep?.signInStep === 'DONE') {
      // Get user details
      console.log('🔄 Getting user details...')
      const user = await getCurrentUser()
      console.log('👤 User details:', {
        userId: user.userId,
        username: user.username,
        loginId: user.signInDetails?.loginId
      })

      return {
        success: true,
        user: {
          userId: user.userId,
          username: user.username,
          email: user.signInDetails?.loginId
        }
      }
    }

    return {
      success: false,
      error: 'Sign in was not completed'
    }
  } catch (error) {
    console.error('Auth error:', error)
    console.error('Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace'
    })

    // Handle specific Cognito error messages
    if (error instanceof Error) {
      switch (error.name) {
        case 'UserNotConfirmedException':
          return {
            success: false,
            error: 'Please verify your email address. Check your email for a confirmation link.'
          }
        case 'NotAuthorizedException':
          return {
            success: false,
            error: 'Incorrect email or password. Please check your credentials and try again.'
          }
        case 'UserNotFoundException':
          return {
            success: false,
            error: 'No account found with this email address. Please check your email or sign up.'
          }
        case 'InvalidParameterException':
          return {
            success: false,
            error: 'Invalid login parameters. Please check your email format.'
          }
        case 'TooManyRequestsException':
          return {
            success: false,
            error: 'Too many login attempts. Please wait a moment and try again.'
          }
        default:
          return {
            success: false,
            error: `Authentication failed: ${error.message}`
          }
      }
    }

    return {
      success: false,
      error: 'An unexpected error occurred during sign in'
    }
  }
}

export async function forgotPassword(email: string): Promise<{ success: boolean; error?: string }> {
  try {
    await amplifyResetPassword({ username: email })
    return { success: true }
  } catch (error) {
    console.error('Forgot password error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to initiate password reset'
    }
  }
}

export async function resetPassword(
  email: string,
  code: string,
  newPassword: string
): Promise<{ success: boolean; error?: string }> {
  try {
    await confirmResetPassword({
      username: email,
      confirmationCode: code,
      newPassword: newPassword
    })
    return { success: true }
  } catch (error) {
    console.error('Reset password error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to reset password'
    }
  }
}

// Sign out function
export async function logoutUser(): Promise<{ success: boolean; error?: string }> {
  try {
    await signOut()
    return { success: true }
  } catch (error) {
    console.error('Logout error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to sign out'
    }
  }
}

// Get current authenticated user
export async function getCurrentAuthUser(): Promise<AuthUser | null> {
  try {
    const user = await getCurrentUser()
    return {
      userId: user.userId,
      username: user.username,
      email: user.signInDetails?.loginId
    }
  } catch (error) {
    console.error('Get current user error:', error)
    return null
  }
}