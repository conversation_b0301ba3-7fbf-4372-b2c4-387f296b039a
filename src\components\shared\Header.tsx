"use client"

import { useAuth } from "@/contexts/auth/AuthContext"
import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { logoutUser } from "@/lib/auth-utils"

export default function Header() {
  const { user, isAuthenticated, refreshUser } = useAuth()
  const router = useRouter()

  const handleLogout = async () => {
    try {
      const result = await logoutUser()
      if (result.success) {
        await refreshUser() // Update auth context
        router.push('/login')
      } else {
        console.error('Logout failed:', result.error)
      }
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  return (
    <header className="w-full py-4 px-6 border-b">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <h1 className="text-2xl font-bold">Smash Music</h1>
        {isAuthenticated && user && (
          <div className="flex items-center gap-4">
            <span className="text-sm">Welcome, {user.username}</span>
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleLogout}
            >
              Logout
            </Button>
          </div>
        )}
      </div>
    </header>
  )
}
