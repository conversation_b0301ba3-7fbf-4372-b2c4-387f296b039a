import { locales, defaultLocale, type Locale } from '@/i18n/config';
import type { StaticI18nConfig, MessageLoader } from './types';

/**
 * Default message loader that imports JSON files from the messages directory
 */
const defaultMessageLoader: MessageLoader = async (locale: Locale) => {
  try {
    const messages = await import(`../../../messages/${locale}.json`);
    return messages.default || messages;
  } catch (error) {
    console.warn(`Failed to load messages for locale: ${locale}`, error);
    // Fallback to default locale if available
    if (locale !== defaultLocale) {
      try {
        const fallbackMessages = await import(`../../../messages/${defaultLocale}.json`);
        return fallbackMessages.default || fallbackMessages;
      } catch (fallbackError) {
        console.error('Failed to load fallback messages', fallbackError);
        return {};
      }
    }
    return {};
  }
};

/**
 * Creates a static i18n configuration with sensible defaults
 */
export function createStaticI18nConfig(overrides: Partial<StaticI18nConfig> = {}): StaticI18nConfig {
  return {
    defaultLocale,
    locales,
    storageKey: 'NEXT_LOCALE',
    showLoadingState: true,
    messageLoader: defaultMessageLoader,
    ...overrides,
  };
}

/**
 * Default configuration instance
 */
export const defaultStaticI18nConfig = createStaticI18nConfig();
