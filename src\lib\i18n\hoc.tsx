'use client';

import { ComponentType } from 'react';
import { StaticI18nProvider } from './provider';
import type { StaticI18nConfig, WithStaticI18nProps } from './types';

/**
 * Higher-order component that wraps a component with static i18n functionality
 * 
 * This allows you to add i18n to any component without modifying the layout
 * or other global files.
 */
export function withStaticI18n<P extends object>(
  Component: ComponentType<P>,
  config?: Partial<StaticI18nConfig>
) {
  const WrappedComponent = (props: P & WithStaticI18nProps) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { locale, ...componentProps } = props;
    
    return (
      <StaticI18nProvider config={config}>
        <Component {...(componentProps as P)} />
      </StaticI18nProvider>
    );
  };

  WrappedComponent.displayName = `withStaticI18n(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Utility function to create a pre-configured HOC
 */
export function createStaticI18nHOC(config: Partial<StaticI18nConfig>) {
  return function <P extends object>(Component: ComponentType<P>) {
    return withStaticI18n(Component, config);
  };
}
