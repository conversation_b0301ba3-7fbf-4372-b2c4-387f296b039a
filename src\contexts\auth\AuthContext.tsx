"use client"

import { createContext, use<PERSON>ontext, useEffect, useState, type <PERSON>actNode } from "react"
import { getCurrentAuthUser, logoutUser, type AuthUser } from "@/lib/auth-utils"
import { initCognitoAuth } from "@/lib/cognito"

interface AuthContextType {
  user: AuthUser | null
  isLoading: boolean
  isAuthenticated: boolean
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user

  // Initialize Amplify and check for existing session
  useEffect(() => {
    console.log('Initializing Cognito Auth...')
    initCognitoAuth()
    checkAuthState()
  }, [])

  async function checkAuthState() {
    try {
      setIsLoading(true)
      const currentUser = await getCurrentAuthUser()
      setUser(currentUser)
    } catch (error) {
      console.error("Auth state check error:", error)
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  async function signOut() {
    try {
      setIsLoading(true)
      await logoutUser()
      setUser(null)
    } catch (error) {
      console.error("Sign out error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  async function refreshUser() {
    try {
      const currentUser = await getCurrentAuthUser()
      setUser(currentUser)
    } catch (error) {
      console.error("Refresh user error:", error)
      setUser(null)
    }
  }

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    signOut,
    refreshUser,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
