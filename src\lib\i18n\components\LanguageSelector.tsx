'use client';

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CheckIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useStaticLocale } from '../hooks';
import type { Locale } from '@/i18n/config';
import { cn } from '@/lib/utils';

interface LanguageSelectorProps {
  /** Custom CSS classes */
  className?: string;
  /** Whether to show loading state */
  showLoading?: boolean;
  /** Custom language labels */
  customLabels?: Partial<Record<Locale, string>>;
  /** Variant style */
  variant?: 'default' | 'minimal' | 'compact';
}

/**
 * Modular language selector component
 * 
 * This component can be used anywhere in the app to provide language switching
 * functionality. It automatically integrates with the static i18n system.
 */
export function LanguageSelector({
  className,
  showLoading = true,
  customLabels = {},
  variant = 'default',
}: LanguageSelectorProps) {
  const t = useTranslations('LanguageToggle');
  const { locale, setLocale, isLoading, availableLocales } = useStaticLocale();

  const getLanguageLabel = (lang: Locale): string => {
    if (customLabels[lang]) {
      return customLabels[lang]!;
    }

    // Fallback to translation keys
    switch (lang) {
      case 'en': return t('english');
      case 'es': return t('spanish');
      case 'fr': return t('french');
      default: return (lang as string).toUpperCase();
    }
  };

  const handleChange = (value: string) => {
    const newLocale = value as Locale;
    setLocale(newLocale);
  };

  const triggerClasses = cn(
    'transition-colors',
    {
      'rounded-sm p-2 bg-background text-foreground hover:bg-muted': variant === 'default',
      'border-none shadow-none bg-transparent': variant === 'minimal',
      'h-8 text-sm': variant === 'compact',
    },
    showLoading && isLoading && 'pointer-events-none opacity-60',
    className
  );

  return (
    <Select value={locale} onValueChange={handleChange} disabled={isLoading}>
      <SelectTrigger className={triggerClasses}>
        <SelectValue placeholder={t('selectLanguage')} />
      </SelectTrigger>
      <SelectContent className="bg-background text-foreground">
        <SelectGroup>
          {availableLocales.map((lang) => (
            <SelectItem
              key={lang}
              value={lang}
              className={cn(
                'flex cursor-default items-center px-3 py-2 text-base',
                'data-[highlighted]:bg-muted'
              )}
            >
              <div className="mr-2 w-[1rem]">
                {lang === locale && (
                  <CheckIcon className="h-5 w-5 text-foreground" />
                )}
              </div>
              <span>{getLanguageLabel(lang)}</span>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}

/**
 * Compact version of the language selector
 */
export function CompactLanguageSelector(props: Omit<LanguageSelectorProps, 'variant'>) {
  return <LanguageSelector {...props} variant="compact" />;
}

/**
 * Minimal version of the language selector
 */
export function MinimalLanguageSelector(props: Omit<LanguageSelectorProps, 'variant'>) {
  return <LanguageSelector {...props} variant="minimal" />;
}
